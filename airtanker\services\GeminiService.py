from main import airtanker_app
import os
from dotenv import load_dotenv
from google import genai
from google.genai import types


class GeminiService:
    """
    Service for interacting with Google's Gemini AI API
    """
    
    def __init__(self):
        load_dotenv()
        try:
            self.api_key = os.getenv("GOOGLE_GEMINI_API_KEY")
            if not self.api_key:
                raise ValueError("GOOGLE_GEMINI_API_KEY not found in environment variables")
            
            self.client = genai.Client(api_key=self.api_key)
            self.model = "gemini-2.5-pro"
            
        except Exception as e:
            airtanker_app.logger.exception("Error initializing GeminiService: %s", str(e))
            raise
    
    def generate_content_stream(self, prompt_text="Hello", thinking_budget=-1):
        """
        Generate content using Gemini AI with streaming response
        
        Args:
            prompt_text (str): The text prompt to send to Gemini
            thinking_budget (int): Thinking budget for the AI (-1 for unlimited)
            
        Returns:
            Generator: Streaming response chunks
        """
        try:
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=prompt_text),
                    ],
                ),
            ]
            
            generate_content_config = types.GenerateContentConfig(
                thinking_config = types.ThinkingConfig(
                    thinking_budget=-1,
                ),
                response_mime_type="application/json",
            )

            return self.client.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=generate_content_config,
            )
            
        except Exception as e:
            airtanker_app.logger.exception("Error generating content with Gemini: %s", str(e))
            raise
    
    def generate_content(self, prompt_text="Hello", thinking_budget=-1):
        """
        Generate content using Gemini AI with single response
        
        Args:
            prompt_text (str): The text prompt to send to Gemini
            thinking_budget (int): Thinking budget for the AI (-1 for unlimited)
            
        Returns:
            str: Complete response text
        """
        try:
            response_text = ""
            for chunk in self.generate_content_stream(prompt_text, thinking_budget):
                if chunk.text:
                    response_text += chunk.text
            return response_text
            
        except Exception as e:
            airtanker_app.logger.exception("Error generating content with Gemini: %s", str(e))
            raise
    
    def print_stream_response(self, prompt_text="Hello", thinking_budget=-1):
        """
        Generate content and print it to terminal in real-time
        
        Args:
            prompt_text (str): The text prompt to send to Gemini
            thinking_budget (int): Thinking budget for the AI (-1 for unlimited)
        """
        try:
            print(f"\n=== Gemini AI Response ===")
            print(f"Prompt: {prompt_text}")
            print(f"Response:")
            
            for chunk in self.generate_content_stream(prompt_text, thinking_budget):
                if chunk.text:
                    print(chunk.text, end="", flush=True)
            
            print(f"\n=== End Response ===\n")
            
        except Exception as e:
            airtanker_app.logger.exception("Error printing stream response: %s", str(e))
            print(f"Error: {str(e)}")
